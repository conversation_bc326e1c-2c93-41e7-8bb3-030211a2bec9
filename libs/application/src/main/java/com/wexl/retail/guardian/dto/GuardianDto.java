package com.wexl.retail.guardian.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.guardian.model.GuardianRole;
import lombok.Builder;

public record GuardianDto() {
  @Builder
  public record Response(
      Long id,
      @JsonProperty("first_name") String firstName,
      @JsonProperty("last_name") String lastName,
      String email,
      @JsonProperty("mobile_number") String mobileNumber,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("relation_type") GuardianRole relationType,
      @JsonProperty("is_primary") Boolean isPrimary,
      @JsonProperty("image_url") String imageUrl,
      @JsonProperty("country_code_id") Long countryCodeId,
      @JsonProperty("occupation") String occupation,
      @JsonProperty("country_code") String countryCode) {}

  @Builder
  public record StudentGuardianResponse(
      @JsonProperty("father_name") String fatherName,
      @<PERSON>sonProperty("mother_name") String motherName,
      @JsonProperty("guardian_name") String guardianName,
      @JsonProperty("mobile_number") String mobileNumber,
      @JsonProperty("mothers_mobile_number") String mothersMobileNumber) {}
}
