package com.wexl.retail.guardian.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.guardian.dto.GuardianDto;
import com.wexl.retail.guardian.dto.GuardianRequest;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.guardian.repository.GuardianRepository;
import com.wexl.retail.mobile.model.CountryCode;
import com.wexl.retail.mobile.repository.CountryCodeRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.storage.StorageService;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GuardianService {

  @Autowired private UserRepository userRepository;
  @Autowired private GuardianRepository guardianRepository;
  @Autowired private CountryCodeRepository countryCodeRepository;
  @Autowired private StudentRepository studentRepository;
  @Autowired private StorageService storageService;
  private static final String STUDENT_NOT_FOUND = "error.StudentNotFound";
  private static final String INVALID_GUARDIAN = "error.InvalidGuardian";

  public void createGuardian(String authUserId, List<GuardianRequest> guardianRequest) {
    var user = validateUser(authUserId);
    Student student = studentRepository.findByUserInfo(user).get();

    student.setGuardians(buildGuardianDetails(student, guardianRequest));
    userRepository.save(user);
  }

  public List<Guardian> buildGuardianDetails(
      Student student, List<GuardianRequest> guardianRequest) {
    validateGuardianForStudent(student, guardianRequest);
    List<Guardian> guardians = new ArrayList<>();
    var existingGuardiansList = student.getGuardians();
    if (existingGuardiansList != null && !existingGuardiansList.isEmpty()) {
      validateIsPrimary(existingGuardiansList, guardianRequest.getFirst());
      guardians.addAll(student.getGuardians());
    }
    guardianRequest.forEach(
        guardian -> {
          var countryCode =
              guardian.getCountryCodeId() != null
                  ? validateCountryCode(guardian.getCountryCodeId())
                  : validateCountryCode(91L);

          guardians.add(
              Guardian.builder()
                  .email(guardian.getEmailId())
                  .firstName(guardian.getFirstName())
                  .lastName(guardian.getLastName())
                  .mobileNumber(guardian.getMobileNumber())
                  .relationType(guardian.getRelationType())
                  .student(student)
                  .isPrimary(guardian.getIsPrimary())
                  .countryCode(countryCode)
                  .occupation(guardian.getOccupation())
                  .imageUrl(guardian.getImageUrl())
                  .build());
        });
    return guardians;
  }

  private CountryCode validateCountryCode(Long countryCodeId) {
    var countryCode = countryCodeRepository.findById(countryCodeId);
    if (countryCode.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.CountryCode");
    }
    return countryCode.get();
  }

  private void validateIsPrimary(
      List<Guardian> existingGuardiansList, GuardianRequest guardianRequest) {
    if (Boolean.TRUE.equals(guardianRequest.getIsPrimary())) {
      existingGuardiansList.forEach(x -> x.setIsPrimary(false));
    }
  }

  private void validateGuardianForStudent(Student student, List<GuardianRequest> guardianRequest) {
    for (GuardianRequest request : guardianRequest) {
      List<Guardian> guardian =
          guardianRepository.findByRelationTypeAndStudent(request.getRelationType(), student);
      if (!guardian.isEmpty()) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, request.getRelationType() + " already exits");
      }
    }
  }

  public List<GuardianDto.Response> fetchGuardiansDetails(String authUserId) {
    var user = validateUser(authUserId);
    Student student = user.getStudentInfo();
    return student.getGuardians().stream()
        .map(
            guardian ->
                GuardianDto.Response.builder()
                    .id(guardian.getId())
                    .email(guardian.getEmail())
                    .firstName(guardian.getFirstName())
                    .mobileNumber(guardian.getMobileNumber())
                    .lastName(guardian.getLastName())
                    .studentId(guardian.getStudent().getId())
                    .relationType(GuardianRole.valueOf(guardian.getRelationType().name()))
                    .isPrimary(guardian.getIsPrimary())
                    .imageUrl(
                        guardian.getImageUrl() == null ? null : fetchImage(guardian.getImageUrl()))
                    .countryCode(
                        Objects.isNull(guardian.getCountryCode())
                            ? null
                            : guardian.getCountryCode().getCode())
                    .occupation(guardian.getOccupation())
                    .countryCodeId(
                        Objects.isNull(guardian.getCountryCode())
                            ? null
                            : guardian.getCountryCode().getId())
                    .build())
        .sorted(Comparator.comparing(GuardianDto.Response::id).reversed())
        .toList();
  }

  private String fetchImage(String path) {
    return (path == null || path.isEmpty())
        ? null
        : storageService.generatePreSignedUrlForFetchImage(path);
  }

  public void editGuardian(GuardianRequest guardianRequest, Long guardiansId, String authUserId) {
    var user = validateUser(authUserId);
    Student student = user.getStudentInfo();
    var existingGuardiansList = student.getGuardians();
    var guardian = validateGuardian(existingGuardiansList, guardiansId);
    validateIsPrimary(existingGuardiansList, guardianRequest);
    guardian.setEmail(guardianRequest.getEmailId());
    guardian.setFirstName(guardianRequest.getFirstName());
    guardian.setLastName(guardianRequest.getLastName());
    guardian.setRelationType(guardianRequest.getRelationType());
    guardian.setMobileNumber(guardianRequest.getMobileNumber());
    guardian.setIsPrimary(guardianRequest.getIsPrimary());
    guardian.setImageUrl(guardianRequest.getImageUrl());
    guardian.setCountryCode(validateCountryCode(guardianRequest.getCountryCodeId()));
    guardian.setOccupation(guardianRequest.getOccupation());
    userRepository.save(user);
  }

  public User validateUser(String authUserId) {
    var user = userRepository.findByAuthUserId(authUserId);
    if (user.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, STUDENT_NOT_FOUND);
    }
    return user.get();
  }

  public String getGuardianName(Guardian guardian) {
    return guardian.getFirstName() + " " + guardian.getLastName();
  }

  private Guardian validateGuardian(List<Guardian> guardianList, Long guardiansId) {
    if (guardianList.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, INVALID_GUARDIAN);
    }
    return guardianList.stream()
        .filter(x -> x.getId().equals(guardiansId))
        .findFirst()
        .orElseThrow(() -> new ApiException(InternalErrorCodes.INVALID_REQUEST, INVALID_GUARDIAN));
  }

  public void deleteGuardian(Long guardiansId, String authUserId) {
    var user = validateUser(authUserId);
    Student student = user.getStudentInfo();
    var guardianList = student.getGuardians();
    var guardian = validateGuardian(guardianList, guardiansId);
    guardianList.remove(guardian);
    userRepository.save(user);
    guardianRepository.deleteById(guardian.getId());
  }
}
